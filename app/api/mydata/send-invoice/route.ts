// app/api/mydata/send-invoice/route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { MyDataService } from '@/lib/mydata/service';
import { createInvoiceXml } from '@/lib/mydata/createInvoiceXml';
import { parseXmlResponse } from '@/lib/mydata/xmlParser';
import type { Database } from '@/types/supabase';

// Define the expected type for createInvoiceXml function
interface CompanySettingsForXml {
  vatNumber: string;
  country?: string | undefined;
  branch?: string | undefined;
  defaultClassificationType?: string | undefined;
  defaultClassificationCategory?: string | undefined;
}

export async function POST(request: Request) {
  try {
    const { invoiceId } = await request.json();

    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }

    // Initialize Supabase client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Check if the invoice has already been submitted
    const { data: existingInvoice } = await supabase
      .from('invoices')
      .select('mark, qr_url')
      .eq('id', invoiceId)
      .eq('status', 'submitted')
      .single();

    if (existingInvoice?.mark) {
      console.log(`Invoice ${invoiceId} already submitted with mark ${existingInvoice.mark}`);
      return NextResponse.json({
        success: true,
        invoiceMark: existingInvoice.mark,
        qrUrl: existingInvoice.qr_url,
        message: 'Invoice was already submitted'
      });
    }

    // Get company settings directly from the database
    const { data: companyData, error: settingsError } = await supabase
      .from('company_settings')
      .select('*')
      .single();

    if (settingsError || !companyData) {
      console.error('Failed to load company settings:', settingsError);
      return NextResponse.json(
        { error: 'Failed to load company settings' },
        { status: 500 }
      );
    }

    // Prepare company settings for XML generation
    const companySettings: CompanySettingsForXml = {
      vatNumber: companyData.vatNumber,
      country: companyData.country || 'GR',
      branch: companyData.branch || '0',
      defaultClassificationType: companyData.defaultClassificationType,
      defaultClassificationCategory: companyData.defaultClassificationCategory
    };

    // Get invoice data
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();

    if (invoiceError || !invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    // Get invoice lines
    const { data: lines, error: linesError } = await supabase
      .from('invoice_lines')
      .select('*')
      .eq('invoice_id', invoiceId)
      .order('line_number');

    if (linesError || !lines || lines.length === 0) {
      return NextResponse.json(
        { error: 'Invoice lines not found' },
        { status: 404 }
      );
    }

    // Get payment methods
    const paymentMethodsResponse = await supabase
      .from('invoice_payment_methods')
      .select('*')
      .eq('invoice_id', invoiceId);

    let paymentMethods = paymentMethodsResponse.data;
    const paymentsError = paymentMethodsResponse.error;

    if (paymentsError) {
      console.error('Error fetching payment methods:', paymentsError);
      return NextResponse.json(
        { error: 'Error fetching payment methods' },
        { status: 500 }
      );
    }

    // If no payment methods found, create a default one
    if (!paymentMethods || paymentMethods.length === 0) {
      console.log('No payment methods found, creating a default one');

      // Create a default payment method (cash payment)
      const { error: createPaymentError } = await supabase
        .from('invoice_payment_methods')
        .insert({
          invoice_id: invoiceId,
          payment_type: 4, // Cash payment
          amount: invoice.total_gross,
          payment_info: 'Default payment method'
        });

      if (createPaymentError) {
        console.error('Failed to create default payment method:', createPaymentError);
        return NextResponse.json(
          { error: 'Failed to create default payment method' },
          { status: 500 }
        );
      }

      // Fetch the newly created payment method
      const { data: newPaymentMethods, error: newPaymentError } = await supabase
        .from('invoice_payment_methods')
        .select('*')
        .eq('invoice_id', invoiceId);

      if (newPaymentError || !newPaymentMethods || newPaymentMethods.length === 0) {
        console.error('Failed to fetch newly created payment method:', newPaymentError);
        return NextResponse.json(
          { error: 'Payment methods not found after creation' },
          { status: 404 }
        );
      }

      // Use the newly created payment methods
      paymentMethods = newPaymentMethods;
    }

    // Adapt the invoice to match the expected format for createInvoiceXml
    // Convert null values to undefined to match the Invoice type
    const adaptedInvoice = {
      ...invoice,
      client_id: invoice.client_id || undefined,
      client_country: invoice.client_country || 'GR',
      currency: invoice.currency || 'EUR',
      created_at: invoice.created_at || undefined,
      updated_at: invoice.updated_at || undefined,
      mark: invoice.mark || undefined,
      qr_url: invoice.qr_url || undefined
    };

    // Adapt invoice lines
    const adaptedLines = lines.map(line => ({
      ...line,
      created_at: line.created_at || undefined
    }));

    // Adapt payment methods
    const adaptedPaymentMethods = paymentMethods.map(method => ({
      ...method,
      payment_info: method.payment_info || undefined,
      created_at: method.created_at || undefined
    }));

    // Update invoice status to pending
    await supabase
      .from('invoices')
      .update({
        status: 'pending',
        updated_at: new Date().toISOString()
      })
      .eq('id', invoiceId);

    // Create XML with the properly structured company settings
    const invoiceXml = createInvoiceXml(adaptedInvoice, adaptedLines, adaptedPaymentMethods, companySettings);

    // Initialize myDATA service with environment
    const environment = process.env.NODE_ENV === 'production'
      ? 'production'
      : 'development';
    const myDataService = new MyDataService(environment);

    // Handle rate limiting with a simple delay during development
    if (process.env.NODE_ENV !== 'production' && process.env.MYDATA_API_DELAY === 'true') {
      console.log('Adding artificial delay to prevent rate limiting (development only)');
      await new Promise(resolve => setTimeout(resolve, 10000)); // 10 second delay
    }

    try {
      // Send invoice to myDATA
      console.log('Sending invoice to myDATA');
      const responseXml = await myDataService.sendInvoice(invoiceXml);

      // Parse response
      const parsedResponse = parseXmlResponse(responseXml);
// ADD THIS DEBUG LOG
console.log('Parsed response status:', parsedResponse.statusCode);
console.log('Parsed response object:', JSON.stringify(parsedResponse, null, 2));

if (parsedResponse.statusCode === 'Success') {
  console.log('SUCCESS PATH - About to return 200 status');


        // Update invoice with mark and QR URL
        await supabase
          .from('invoices')
          .update({
            mark: parsedResponse.invoiceMark,
            qr_url: parsedResponse.qrUrl,
            status: 'submitted',
            updated_at: new Date().toISOString()
          })
          .eq('id', invoiceId);

        return NextResponse.json({
          success: true,
          invoiceMark: parsedResponse.invoiceMark,
          qrUrl: parsedResponse.qrUrl
        }, { status: 200 }); // This is the correct status code for success
      } else {
         console.log('ERROR PATH - About to return 400 status');
        // Update invoice status to error
        await supabase
          .from('invoices')
          .update({
            status: 'error',
            error_message: JSON.stringify(parsedResponse.errors),
            updated_at: new Date().toISOString()
          })
          .eq('id', invoiceId);

        return NextResponse.json({
          success: false,
          errors: parsedResponse.errors
        }, { status: 400 });
      }
    } catch (apiError: unknown) {
      const error = apiError as { response?: { status: number; headers: Record<string, string> }; message?: string };

      // Check if it's a rate limiting error
      if (error.response && error.response.status === 429) {
        const retryAfter = error.response.headers['retry-after']
          ? parseInt(error.response.headers['retry-after'], 10)
          : 60; // Default 60 seconds

        // Update invoice status to 'rate_limited' for later retry
        await supabase
          .from('invoices')
          .update({
            status: 'rate_limited',
            error_message: `Rate limit exceeded. Try again after ${retryAfter} seconds.`,
            retry_after: new Date(Date.now() + (retryAfter * 1000)).toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', invoiceId);

        return NextResponse.json({
          success: false,
          error: 'Rate limit exceeded',
          retryAfter: retryAfter,
          message: 'Invoice submission has been queued for later retry'
        }, { status: 429 });
      }

      // For other API errors
      await supabase
        .from('invoices')
        .update({
          status: 'error',
          error_message: error.message || 'Unknown API error',
          updated_at: new Date().toISOString()
        })
        .eq('id', invoiceId);

      return NextResponse.json({
        success: false,
        error: error.message || 'Unknown API error'
      }, { status: 500 });
    }
  } catch (error: unknown) {
    console.error('Error sending invoice to myDATA:', error);

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}