'use client';

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X, Filter } from "lucide-react";
import { useState } from "react";
import {
  EXPERTISE_LEVELS,
  MOVEMENT_CATEGORIES,
  BODY_PARTS,
  EQUIPMENT,
  VIDEO_SOURCES,
  type Filters
} from './ExercisesClient';

interface ExerciseFiltersProps {
  searchTerm: string;
  filters: Filters;
  onSearchChange: (value: string) => void;
  onFilterChange: (filters: Partial<Filters>) => void;
  onClearFilters: () => void;
  disabled?: boolean;
}

export default function ExerciseFilters({
  searchTerm,
  filters,
  onFilterChange,
  onClearFilters,
  disabled = false
}: ExerciseFiltersProps) {
  const [showFilters, setShowFilters] = useState(false);

  // Count active filters
  const activeFilterCount = Object.values(filters).filter(value => value !== 'all').length;
  const hasActiveFilters = activeFilterCount > 0 || searchTerm;

  // Handle individual filter changes
  const handleFilterChange = (key: keyof Filters, value: string) => {
    onFilterChange({ [key]: value });
  };

  // Clear individual filter
  const clearFilter = (key: keyof Filters) => {
    onFilterChange({ [key]: 'all' });
  };

  return (
    <Card className="p-4 mb-6">
      {/* Filter Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            disabled={disabled}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-1">
                {activeFilterCount}
              </Badge>
            )}
          </Button>

          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              disabled={disabled}
              className="text-red-600 hover:text-red-700"
            >
              Clear All
            </Button>
          )}
        </div>

        {/* Active Filters Preview */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-1">
            {searchTerm && (
              <Badge variant="outline" className="flex items-center gap-1">
                Search: &quot;{searchTerm}&quot;
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0"
                  onClick={() => onSearchChange('')}
                  disabled={disabled}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}

            {Object.entries(filters).map(([key, value]) => {
              if (value === 'all') return null;

              const displayValue = value === 'yes' ? 'Yes' : value === 'no' ? 'No' : value;
              const filterKey = key as keyof Filters;

              return (
                <Badge key={key} variant="outline" className="flex items-center gap-1">
                  {key.replace('_', ' ')}: {displayValue}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0"
                    onClick={() => clearFilter(filterKey)}
                    disabled={disabled}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              );
            })}
          </div>
        )}
      </div>

      {/* Filter Controls */}
      {showFilters && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Body Part Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Body Part</label>
            <Select
              value={filters.body_part}
              onValueChange={(value) => handleFilterChange('body_part', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Body Parts" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Body Parts</SelectItem>
                {BODY_PARTS.map((part) => (
                  <SelectItem key={part} value={part}>
                    {part}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Equipment Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Equipment</label>
            <Select
              value={filters.equipment}
              onValueChange={(value) => handleFilterChange('equipment', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Equipment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Equipment</SelectItem>
                {EQUIPMENT.map((item) => (
                  <SelectItem key={item} value={item}>
                    {item}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Expertise Level Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Expertise Level</label>
            <Select
              value={filters.expertise_level}
              onValueChange={(value) => handleFilterChange('expertise_level', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Levels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                {EXPERTISE_LEVELS.map((level) => (
                  <SelectItem key={level} value={level}>
                    {level}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Movement Category Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Movement Category</label>
            <Select
              value={filters.movement_category}
              onValueChange={(value) => handleFilterChange('movement_category', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {MOVEMENT_CATEGORIES.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Has Video Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Has Video</label>
            <Select
              value={filters.has_video}
              onValueChange={(value) => handleFilterChange('has_video', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any</SelectItem>
                <SelectItem value="yes">Yes</SelectItem>
                <SelectItem value="no">No</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Has Image Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Has Image</label>
            <Select
              value={filters.has_image}
              onValueChange={(value) => handleFilterChange('has_image', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any</SelectItem>
                <SelectItem value="yes">Yes</SelectItem>
                <SelectItem value="no">No</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Has Records Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Has Records</label>
            <Select
              value={filters.has_records}
              onValueChange={(value) => handleFilterChange('has_records', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any</SelectItem>
                <SelectItem value="yes">Yes</SelectItem>
                <SelectItem value="no">No</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Video Source Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Video Source</label>
            <Select
              value={filters.video_source}
              onValueChange={(value) => handleFilterChange('video_source', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Sources" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                {VIDEO_SOURCES.map((source) => (
                  <SelectItem key={source} value={source}>
                    {source}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </Card>
  );
}