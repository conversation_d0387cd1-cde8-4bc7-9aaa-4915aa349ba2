'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronDown, ChevronUp, ChevronsUpDown, Info } from 'lucide-react';
import { ExerciseMovement, SortConfig } from './ExercisesClient';
import MediaIndicator from './MediaIndicator';
import SafeImage from '@/components/ui/SafeImage';
import { getDirectImageUrl, getImageUrl } from '@/lib/utils/imageUtils';

interface ExerciseTableProps {
  exercises: ExerciseMovement[];
  sortConfig: SortConfig;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  totalExercises: number;
  totalAllExercises: number;
  onSort: (key: string) => void;
  onEdit: (exercise: ExerciseMovement) => void;
  onDelete: (id: string) => void;
  onOpenVideoPreview: (url: string, source: string) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  isLoading?: boolean;
}

export default function ExerciseTable({
  exercises,
  sortConfig,
  pageSize,
  totalExercises,
  totalAllExercises,
  onSort,
  onEdit,
  onDelete,
  onOpenVideoPreview,
  onPageSizeChange,
  isLoading = false,
}: ExerciseTableProps) {

  // Get sort direction icon
  const getSortIcon = (key: string) => {
    if (sortConfig?.key !== key) return <ChevronsUpDown className="h-4 w-4" />;
    return sortConfig.direction === 'asc'
      ? <ChevronUp className="h-4 w-4" />
      : <ChevronDown className="h-4 w-4" />;
  };

  // Helper to get primary category value
  const getPrimaryCategoryValue = (exercise: ExerciseMovement, categoryType: string) => {
    return exercise.categories?.find(cat =>
      cat.category_type === categoryType && cat.is_primary
    )?.category_value || '';
  };

  // Helper to get primary image
  const getPrimaryImage = (exercise: ExerciseMovement) => {
    return exercise.images?.find(image => image.is_primary) || exercise.images?.[0];
  };

  // Helper to get image URL with fallback logic
  const getExerciseImageUrl = (exercise: ExerciseMovement): string | null => {
    const primaryImage = getPrimaryImage(exercise);
    if (!primaryImage) return null;

    // Try image_url first (if it exists in your database)
    if (primaryImage.image_url) {
      return getDirectImageUrl(primaryImage.image_url);
    }

    // Fallback to image_id
    if (primaryImage.image_id) {
      return getImageUrl(primaryImage.image_id);
    }

    return null;
  };

  return (
    <div>
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white/50 z-20 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Loading exercises...</p>
          </div>
        </div>
      )}

      {/* Simple table without virtualization */}
      <div className="border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full caption-bottom text-sm">
            {/* Table header */}
            <thead className="[&_tr]:border-b bg-gray-50">
              <tr className="border-b transition-colors hover:bg-muted/50">
                <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                  Thumbnail
                </th>
                <th
                  className="h-12 px-4 text-left align-middle font-medium text-muted-foreground cursor-pointer hover:bg-gray-100"
                  onClick={() => onSort('exercise_name')}
                >
                  <div className="flex items-center gap-2">
                    Exercise Name
                    {getSortIcon('exercise_name')}
                  </div>
                </th>
                <th
                  className="h-12 px-4 text-left align-middle font-medium text-muted-foreground cursor-pointer hover:bg-gray-100"
                  onClick={() => onSort('body_part')}
                >
                  <div className="flex items-center gap-2">
                    Body Part
                    {getSortIcon('body_part')}
                  </div>
                </th>
                <th
                  className="h-12 px-4 text-left align-middle font-medium text-muted-foreground cursor-pointer hover:bg-gray-100"
                  onClick={() => onSort('equipment')}
                >
                  <div className="flex items-center gap-2">
                    Equipment
                    {getSortIcon('equipment')}
                  </div>
                </th>
                <th
                  className="h-12 px-4 text-left align-middle font-medium text-muted-foreground cursor-pointer hover:bg-gray-100"
                  onClick={() => onSort('expertise_level')}
                >
                  <div className="flex items-center gap-2">
                    Expertise Level
                    {getSortIcon('expertise_level')}
                  </div>
                </th>
                <th
                  className="h-12 px-4 text-left align-middle font-medium text-muted-foreground cursor-pointer hover:bg-gray-100"
                  onClick={() => onSort('movement_category')}
                >
                  <div className="flex items-center gap-2">
                    Movement Category
                    {getSortIcon('movement_category')}
                  </div>
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                  Media
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                  Actions
                </th>
              </tr>
            </thead>

            {/* Table body */}
            <tbody className="[&_tr:last-child]:border-0">
              {exercises.map((exercise) => {
                const imageUrl = getExerciseImageUrl(exercise);
                const primaryImage = getPrimaryImage(exercise);

                return (
                  <tr
                    key={exercise.id}
                    className={`border-b transition-colors hover:bg-muted/50 ${
                      exercise.hasRecords
                        ? "bg-blue-50 border-l-4 border-l-blue-500"
                        : ""
                    }`}
                  >
                    {/* Thumbnail cell */}
                    <td className="p-4">
                      <div className="w-16">
                        {imageUrl ? (
                          <div>
                            <SafeImage
                              src={imageUrl}
                              alt={exercise.exercise_name}
                              width={48}
                              height={48}
                              className="rounded-md object-cover"
                              sizes="48px"
                            />
                            {/* Debug info */}
                            <div className="mt-1 text-xs text-gray-500 truncate"
                                 title={primaryImage?.image_id || primaryImage?.image_url || ''}>
                              {primaryImage?.image_id
                                ? `ID: ${primaryImage.image_id.substring(0, 10)}...`
                                : 'URL'}
                            </div>
                          </div>
                        ) : (
                          <div className="w-12 h-12 bg-gray-100 flex items-center justify-center rounded-md">
                            <span className="text-xs text-gray-400">No image</span>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Exercise name cell */}
                    <td className="p-4">
                      <div className="flex flex-col">
                        <span className="font-medium">{exercise.exercise_name}</span>
                        {exercise.hasRecords && (
                          <Badge variant="outline" className="mt-1 bg-blue-100 text-blue-800 font-medium text-xs w-fit">
                            Has Records
                          </Badge>
                        )}
                      </div>
                    </td>

                    {/* Category cells */}
                    <td className="p-4">{getPrimaryCategoryValue(exercise, 'body_part')}</td>
                    <td className="p-4">{getPrimaryCategoryValue(exercise, 'equipment')}</td>
                    <td className="p-4">{getPrimaryCategoryValue(exercise, 'expertise_level')}</td>
                    <td className="p-4">{getPrimaryCategoryValue(exercise, 'movement_category')}</td>

                    {/* Media cell */}
                    <td className="p-4">
                      <MediaIndicator
                        videos={exercise.videos || []}
                        images={exercise.images || []}
                        onVideoClick={(video) => onOpenVideoPreview(video.video_url, video.video_source || '')}
                      />
                    </td>

                    {/* Actions cell */}
                    <td className="p-4">
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(exercise)}
                          disabled={isLoading}
                        >
                          Edit
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="destructive"
                              size="sm"
                              disabled={exercise.hasRecords || isLoading}
                              title={exercise.hasRecords ? "Cannot delete exercises with records" : "Delete exercise"}
                            >
                              Delete
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Exercise</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete &quot;{exercise.exercise_name}&quot;? This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => onDelete(exercise.id)}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Empty state */}
        {exercises.length === 0 && !isLoading && (
          <div className="text-center py-12 text-gray-500">
            <p className="text-lg font-medium">No exercises found</p>
            <p className="text-sm mt-1">Try adjusting your search criteria or filters.</p>
          </div>
        )}
      </div>

      {/* Pagination controls */}
      {totalExercises > 0 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing {totalExercises} filtered exercises (from total {totalAllExercises})
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Display Count:</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => onPageSizeChange(Number(value))}
              disabled={isLoading}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
                <SelectItem value="500">500</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Info message */}
      <div className="mt-4 text-sm text-gray-700 flex items-center">
        <Info className="h-4 w-4 mr-2 flex-shrink-0" />
        Exercises with <span className="bg-blue-50 border-l-4 border-blue-500 px-1 rounded mx-1">blue background and left border</span> have associated performance records and cannot be deleted.
      </div>
    </div>
  );
}